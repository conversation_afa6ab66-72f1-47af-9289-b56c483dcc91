// src/pages/Home.tsx
import { Box, Heading, Text, SimpleGrid, VStack } from "@chakra-ui/react";
import Navbar from "@/Component/NavBar";
import BookingForm from "@/Component/BookingForm";
import FeaturedTours from "@/Component/FeaturedTour";
import Activities from "@/Component/Activities";
import Footer from "@/Component/Footer";
import Hero from "@/Component/Hero";
import WhatsAppFloat from "@/Component/WhatsAppFloat";

export default function Home() {
  return (
    <Box>
      <Navbar />
      <Box as="main" p={1} maxW="100%" mx="auto">
        <Hero />
        <Box as="section" id="tours">
          <FeaturedTours />
        </Box>
        <Box as="section" id="activities">
          <Activities />
        </Box>

        <Box as="section" id="booking" bg="gray.50" p={8} borderRadius="lg">
          <Heading
            color="blackAlpha.800"
            as="h2"
            size="2xl"
            mb={6}
            textAlign={"center"}
          >
            Book Your Adventure
          </Heading>
          <BookingForm />
        </Box>
      </Box>
      <Footer />

      {/* WhatsApp Floating Button */}
      <WhatsAppFloat />
    </Box>
  );
}
