// src/pages/Home.tsx
import { Box, Heading, Text, SimpleGrid, VStack } from "@chakra-ui/react";
import Navbar from "@/Component/NavBar";
import BookingForm from "@/Component/BookingForm";
import FeaturedTours from "@/Component/FeaturedTour";
import Activities from "@/Component/Activities";
import Footer from "@/Component/Footer";
import Hero from "@/Component/Hero";
import FloatingSocial from "@/Component/FloatingSocial";
import { useColorMode } from "@/components/ui/color-mode";
import { useEffect } from "react";

export default function Home() {
  const { setColorMode } = useColorMode();

  // Set default dark mode on component mount
  useEffect(() => {
    setColorMode("dark");
  }, [setColorMode]);

  return (
    <Box>
      <Navbar />
      <Box as="main" p={1} maxW="100%" mx="auto">
        <Hero />
        <Box as="section" id="tours">
          <FeaturedTours />
        </Box>
        <Box as="section" id="activities">
          <Activities />
        </Box>

        {/* About Section */}
        <Box
          as="section"
          id="about"
          py={16}
          bg={{ base: "gray.100", _dark: "gray.800" }}
        >
          <Box maxW="1200px" mx="auto" px={4}>
            <VStack gap={8}>
              <Heading
                as="h2"
                size="3xl"
                textAlign="center"
                color={{ base: "gray.900", _dark: "white" }}
                mb={4}
              >
                About Seven Sev
              </Heading>
              <Text
                fontSize="xl"
                textAlign="center"
                color={{ base: "gray.600", _dark: "gray.300" }}
                maxW="3xl"
                lineHeight="tall"
              >
                With over 15 years of experience, Safari Tanzania is your
                trusted partner for unforgettable African adventures. We
                specialize in creating personalized safari experiences that
                showcase the incredible wildlife, stunning landscapes, and rich
                cultures of Tanzania.
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} gap={8} w="full" mt={8}>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    500+
                  </Heading>
                  <Text color={{ base: "gray.600", _dark: "gray.300" }}>
                    Happy Travelers
                  </Text>
                </VStack>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    15+
                  </Heading>
                  <Text color={{ base: "gray.600", _dark: "gray.300" }}>
                    Years Experience
                  </Text>
                </VStack>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    50+
                  </Heading>
                  <Text color={{ base: "gray.600", _dark: "gray.300" }}>
                    Tour Packages
                  </Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </Box>
        </Box>

        <Box
          as="section"
          id="booking"
          bg={{ base: "gray.50", _dark: "gray.700" }}
          p={8}
          borderRadius="lg"
        >
          <Heading
            color={{ base: "blackAlpha.800", _dark: "white" }}
            as="h2"
            size="2xl"
            mb={6}
            textAlign={"center"}
          >
            Book Your Adventure
          </Heading>
          <BookingForm />
        </Box>
      </Box>
      <Footer />

      {/* Floating Social Media Buttons */}
      <FloatingSocial />
    </Box>
  );
}
