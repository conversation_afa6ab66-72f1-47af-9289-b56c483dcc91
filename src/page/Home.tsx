// src/pages/Home.tsx
import { Box, Heading, Text, SimpleGrid, VStack } from "@chakra-ui/react";
import Navbar from "@/Component/NavBar";
import BookingForm from "@/Component/BookingForm";
import FeaturedTours from "@/Component/FeaturedTour";
import Activities from "@/Component/Activities";
import Footer from "@/Component/Footer";
import Hero from "@/Component/Hero";
import WhatsAppFloat from "@/Component/WhatsAppFloat";

export default function Home() {
  return (
    <Box>
      <Navbar />
      <Box as="main" p={1} maxW="100%" mx="auto">
        <Hero />
        <Box as="section" id="tours">
          <FeaturedTours />
        </Box>
        <Box as="section" id="activities">
          <Activities />
        </Box>

        {/* About Section */}
        <Box as="section" id="about" py={16} bg="white">
          <Box maxW="1200px" mx="auto" px={4}>
            <VStack gap={8}>
              <Heading
                as="h2"
                size="3xl"
                textAlign="center"
                color="gray.900"
                mb={4}
              >
                About Seven Sev
              </Heading>
              <Text
                fontSize="xl"
                textAlign="center"
                color="gray.600"
                maxW="3xl"
                lineHeight="tall"
              >
                With over 15 years of experience, Safari Tanzania is your
                trusted partner for unforgettable African adventures. We
                specialize in creating personalized safari experiences that
                showcase the incredible wildlife, stunning landscapes, and rich
                cultures of Tanzania.
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} gap={8} w="full" mt={8}>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    500+
                  </Heading>
                  <Text color="gray.600">Happy Travelers</Text>
                </VStack>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    15+
                  </Heading>
                  <Text color="gray.600">Years Experience</Text>
                </VStack>
                <VStack gap={4} textAlign="center">
                  <Heading size="lg" color="green.600">
                    50+
                  </Heading>
                  <Text color="gray.600">Tour Packages</Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </Box>
        </Box>

        <Box as="section" id="booking" bg="gray.50" p={8} borderRadius="lg">
          <Heading
            color="blackAlpha.800"
            as="h2"
            size="2xl"
            mb={6}
            textAlign={"center"}
          >
            Book Your Adventure
          </Heading>
          <BookingForm />
        </Box>
      </Box>
      <Footer />

      {/* WhatsApp Floating Button */}
      <WhatsAppFloat />
    </Box>
  );
}
