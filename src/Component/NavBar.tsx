import {
  Flex,
  IconButton,
  Link,
  <PERSON><PERSON>,
  useDisclosure,
  Box,
  Stack,
  Collapsible,
  Icon,
} from "@chakra-ui/react";
import { useState } from "react";
import { FaChevronDown } from "react-icons/fa";
import { FiMenu as HamburgerIcon } from "react-icons/fi";

const Navbar = () => {
  const { open, onToggle } = useDisclosure();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const navItems = [
    { name: "Home", link: "/" },
    {
      name: "Destinations",
      subItems: ["Serengeti", "Zanzibar", "Kilimanjaro"],
    },
    {
      name: "Tours",
      subItems: ["Safari", "Beach", "Cultural"],
    },
    { name: "Activities", link: "#activities" },
    { name: "About", link: "#about" },
    { name: "Book Now", link: "#booking" },
  ];

  return (
    <Box
      bg="white"
      color="gray.950"
      boxShadow="md"
      position="sticky"
      top={0}
      zIndex={10}
    >
      <Flex
        as="nav"
        p={4}
        justify="space-between"
        align="center"
        maxW="1200px"
        mx="auto"
        w="100%"
      >
        <Link
          href="/"
          fontSize="2xl"
          fontFamily="heading"
          fontWeight="bold"
          color="gray.900"
        >
          Safari Tanzania
        </Link>

        {/* Desktop Navigation */}
        <Flex display={{ base: "none", md: "flex" }} align="center" gap={6}>
          {navItems.map((item) => (
            <Box
              key={item.name}
              position="relative"
              onMouseLeave={() => setActiveDropdown(null)}
            >
              {item.subItems ? (
                <Collapsible.Root open={activeDropdown === item.name}>
                  <Button
                    variant="ghost"
                    _hover={{ color: "green.600" }}
                    onClick={() =>
                      setActiveDropdown(
                        activeDropdown === item.name ? null : item.name
                      )
                    }
                  >
                    {item.name}
                    <Icon as={FaChevronDown} ml={2} />
                  </Button>

                  <Collapsible.Content
                    position="absolute"
                    bg="white"
                    boxShadow="xl"
                    p={2}
                    borderRadius="md"
                    minW="200px"
                    zIndex={1}
                  >
                    <Stack>
                      {item.subItems.map((sub) => (
                        <Link
                          key={sub}
                          href={`#${sub.toLowerCase()}`}
                          px={3}
                          py={2}
                          _hover={{ bg: "gray.100", color: "green.600" }}
                        >
                          {sub}
                        </Link>
                      ))}
                    </Stack>
                  </Collapsible.Content>
                </Collapsible.Root>
              ) : (
                <Link
                  href={item.link}
                  px={2}
                  py={1}
                  _hover={{ color: "green.600" }}
                >
                  {item.name}
                </Link>
              )}
            </Box>
          ))}
        </Flex>

        {/* Mobile Navigation */}
        <IconButton
          display={{ md: "none" }}
          onClick={onToggle}
          aria-label="Toggle menu"
          variant="outline"
        >
          <HamburgerIcon />
        </IconButton>
      </Flex>

      {/* Mobile Menu */}
      <Collapsible.Root open={open}>
        <Collapsible.Content>
          <Stack bg="white" p={4} display={{ md: "none" }}>
            {navItems.map((item) => (
              <Box key={item.name}>
                {item.subItems ? (
                  <Collapsible.Root open={activeDropdown === item.name}>
                    <Button
                      w="full"
                      justifyContent="space-between"
                      variant="ghost"
                      onClick={() =>
                        setActiveDropdown(
                          activeDropdown === item.name ? null : item.name
                        )
                      }
                    >
                      {item.name}
                      <Icon as={FaChevronDown} ml={2} />
                    </Button>

                    <Collapsible.Content pl={4}>
                      <Stack>
                        {item.subItems.map((sub) => (
                          <Link
                            key={sub}
                            href={`#${sub.toLowerCase()}`}
                            py={2}
                            _hover={{ color: "green.600" }}
                          >
                            {sub}
                          </Link>
                        ))}
                      </Stack>
                    </Collapsible.Content>
                  </Collapsible.Root>
                ) : (
                  <Link
                    href={item.link}
                    display="block"
                    py={2}
                    _hover={{ color: "green.600" }}
                  >
                    {item.name}
                  </Link>
                )}
              </Box>
            ))}
          </Stack>
        </Collapsible.Content>
      </Collapsible.Root>
    </Box>
  );
};

export default Navbar;
