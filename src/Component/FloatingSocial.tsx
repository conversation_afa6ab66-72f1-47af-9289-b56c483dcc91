import { Box, IconButton, Text, VStack } from "@chakra-ui/react";
import { useState } from "react";
import { 
  FaWhatsapp, 
  FaInstagram, 
  FaTiktok, 
  FaPhone, 
  FaEnvelope,
  FaTimes,
  FaPlus
} from "react-icons/fa";

const FloatingSocial = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleWhatsApp = () => {
    const message = "Hello! I'm interested in your safari tours. Can you provide more information?";
    const phoneNumber = "255123456789";
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, "_blank");
  };

  const handleInstagram = () => {
    window.open("https://instagram.com/safaritanzania", "_blank");
  };

  const handleTikTok = () => {
    window.open("https://tiktok.com/@safaritanzania", "_blank");
  };

  const handleCall = () => {
    window.open("tel:+255123456789", "_self");
  };

  const handleEmail = () => {
    window.open("mailto:<EMAIL>", "_self");
  };

  const socialButtons = [
    {
      icon: FaWhatsapp,
      label: "WhatsApp Us",
      bg: "#25D366",
      hoverBg: "#128C7E",
      onClick: handleWhatsApp,
    },
    {
      icon: FaInstagram,
      label: "Follow us!",
      bg: "#E4405F",
      hoverBg: "#C13584",
      onClick: handleInstagram,
    },
    {
      icon: FaTiktok,
      label: "TikTok",
      bg: "#000000",
      hoverBg: "#333333",
      onClick: handleTikTok,
    },
    {
      icon: FaPhone,
      label: "Call Us",
      bg: "#8B4513",
      hoverBg: "#654321",
      onClick: handleCall,
    },
    {
      icon: FaEnvelope,
      label: "Email Us",
      bg: "#D2B48C",
      hoverBg: "#DEB887",
      onClick: handleEmail,
      textColor: "#8B4513",
    },
  ];

  return (
    <Box
      position="fixed"
      bottom="20px"
      right="20px"
      zIndex={1000}
      display="flex"
      flexDirection="column"
      alignItems="flex-end"
      gap={3}
    >
      {/* Social Media Buttons */}
      {isOpen && (
        <VStack gap={3} align="flex-end">
          {socialButtons.map((button, index) => (
            <Box
              key={index}
              display="flex"
              alignItems="center"
              gap={3}
              opacity={0}
              transform="translateX(100px)"
              animation={`slideIn 0.3s ease-out ${index * 0.1}s forwards`}
              css={{
                "@keyframes slideIn": {
                  to: {
                    opacity: 1,
                    transform: "translateX(0)",
                  },
                },
              }}
            >
              <Box
                bg={button.bg}
                color={button.textColor || "white"}
                px={6}
                py={3}
                borderRadius="full"
                display="flex"
                alignItems="center"
                gap={3}
                cursor="pointer"
                onClick={button.onClick}
                _hover={{
                  bg: button.hoverBg,
                  transform: "scale(1.05)",
                }}
                transition="all 0.2s ease-in-out"
                boxShadow="lg"
                minW="150px"
              >
                <button.icon size="20px" />
                <Text fontWeight="medium" fontSize="sm">
                  {button.label}
                </Text>
              </Box>
            </Box>
          ))}
          
          {/* Close Button */}
          <Box
            display="flex"
            alignItems="center"
            gap={3}
            opacity={0}
            transform="translateX(100px)"
            animation={`slideIn 0.3s ease-out ${socialButtons.length * 0.1}s forwards`}
            css={{
              "@keyframes slideIn": {
                to: {
                  opacity: 1,
                  transform: "translateX(0)",
                },
              },
            }}
          >
            <Box
              bg="#8B4513"
              color="white"
              px={6}
              py={3}
              borderRadius="full"
              display="flex"
              alignItems="center"
              gap={3}
              cursor="pointer"
              onClick={() => setIsOpen(false)}
              _hover={{
                bg: "#654321",
                transform: "scale(1.05)",
              }}
              transition="all 0.2s ease-in-out"
              boxShadow="lg"
              minW="150px"
            >
              <FaTimes size="20px" />
              <Text fontWeight="medium" fontSize="sm">
                Close
              </Text>
            </Box>
          </Box>
        </VStack>
      )}

      {/* Main Toggle Button */}
      <IconButton
        aria-label="Toggle social media menu"
        onClick={() => setIsOpen(!isOpen)}
        size="lg"
        borderRadius="full"
        bg="#25D366"
        color="white"
        _hover={{
          bg: "#128C7E",
          transform: "scale(1.1)",
        }}
        _active={{
          bg: "#075E54",
          transform: "scale(0.95)",
        }}
        boxShadow="lg"
        transition="all 0.2s ease-in-out"
        w="60px"
        h="60px"
        fontSize="24px"
        css={{
          animation: isOpen ? "none" : "pulse 2s infinite",
          "@keyframes pulse": {
            "0%": {
              transform: "scale(1)",
              boxShadow: "0 0 0 0 rgba(37, 211, 102, 0.7)",
            },
            "70%": {
              transform: "scale(1.05)",
              boxShadow: "0 0 0 10px rgba(37, 211, 102, 0)",
            },
            "100%": {
              transform: "scale(1)",
              boxShadow: "0 0 0 0 rgba(37, 211, 102, 0)",
            },
          },
        }}
      >
        {isOpen ? <FaTimes /> : <FaPlus />}
      </IconButton>
    </Box>
  );
};

export default FloatingSocial;
