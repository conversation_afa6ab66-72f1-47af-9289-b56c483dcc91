"use client";
import { useForm as Form } from "@formspree/react";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Button,
  Input,
  Select,
  Textarea,
  Field,
  Box,
  SimpleGrid,
  Portal,
  createListCollection,
} from "@chakra-ui/react";
import { toaster } from "@/components/ui/toaster";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";

type FormData = {
  name: string;
  email: string;
  phone: string;
  destination: string;
  tourType: string;
  participants: number;
  startDate: string;
  message?: string;
};

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(6, "Invalid phone number"),
  destination: z.string().min(1, "Please select a destination"),
  tourType: z.string().min(1, "Please select a tour type"),
  participants: z.number().min(1, "Minimum 1 participant"),
  startDate: z.string().min(1, "Please select a date"),
  message: z.string().optional(),
});

const destinations = createListCollection({
  items: [
    { label: "Serengeti National Park", value: "serengeti" },
    { label: "Zanzibar Island", value: "zanzibar" },
    { label: "Mount Kilimanjaro", value: "kilimanjaro" },
    { label: "Ngorongoro Crater", value: "ngorongoro" },
    { label: "Tarangire National Park", value: "tarangire" },
    { label: "Lake Manyara", value: "manyara" },
  ],
});

const tourTypes = createListCollection({
  items: [
    { label: "Wildlife Safari", value: "safari" },
    { label: "Beach Holiday", value: "beach" },
    { label: "Mountain Climbing", value: "mountain" },
    { label: "Cultural Tour", value: "cultural" },
    { label: "Photography Tour", value: "photography" },
    { label: "Luxury Safari", value: "luxury" },
  ],
});

const BookingForm = () => {
  const [state, handleFormspreeSubmit] = Form("xqaqlwnv");
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  const onSubmit = async (data: FormData) => {
    try {
      // Submit to Formspree
      await handleFormspreeSubmit(data);
      // WhatsApp integration
      const whatsappMessage = `New Booking Request:%0A%0A
        Name: ${data.name}%0A
        Email: ${data.email}%0A
        Phone: ${data.phone}%0A
        Destination: ${data.destination}%0A
        Tour Type: ${data.tourType}%0A
        Participants: ${data.participants}%0A
        Start Date: ${data.startDate}%0A
        Message: ${data.message || "None"}`;

      window.open(
        `https://wa.me/255123456789?text=${encodeURIComponent(whatsappMessage)}`
      );

      toaster.create({
        title: "Request Sent!",
        description: "We'll contact you within 24 hours",
        type: "success",
        duration: 5000,
      });
    } catch {
      toaster.create({
        title: "Error",
        description: "Failed to submit form",
        type: "error",
      });
    }
  };

  return (
    <Box maxW="48rem" mx="auto" p={12} borderRadius="lg" bg="white">
      <form onSubmit={handleSubmit(onSubmit)}>
        <SimpleGrid columns={{ base: 1, md: 2 }} spaceX={3}>
          <Field.Root invalid={!!errors.name} mb={6}>
            <Field.Label color="blackAlpha.800">Full Name</Field.Label>
            <Input {...register("name")} placeholder="John Doe" />
            <Field.ErrorText fontSize={15}>
              {errors.name?.message}
            </Field.ErrorText>
          </Field.Root>
          <Field.Root invalid={!!errors.email} mb={6}>
            <Field.Label color="blackAlpha.800">Email</Field.Label>
            <Input
              type="email"
              {...register("email")}
              placeholder="<EMAIL>"
            />
            <Field.ErrorText fontSize={15}>
              {errors.email?.message}
            </Field.ErrorText>
          </Field.Root>
          <Field.Root invalid={!!errors.phone} mb={6}>
            <Field.Label color="blackAlpha.800">Phone</Field.Label>
            <Input {...register("phone")} placeholder="+255 123 456 789" />
            <Field.ErrorText fontSize={15}>
              {errors.phone?.message}
            </Field.ErrorText>
          </Field.Root>
          <Field.Root invalid={!!errors.destination} mb={6}>
            <Field.Label color="blackAlpha.800">Destination</Field.Label>
            <Controller
              name="destination"
              control={control}
              render={({ field }) => (
                <Select.Root
                  key={field.value}
                  collection={destinations}
                  onValueChange={field.onChange}
                >
                  <Select.Trigger>
                    <Select.ValueText placeholder="Select destination" />
                  </Select.Trigger>
                  <Portal>
                    <Select.Positioner>
                      <Select.Content>
                        {destinations.items.map((destination) => (
                          <Select.Item
                            key={destination.value}
                            item={destination}
                          >
                            {destination.label}
                            <Select.ItemIndicator />
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select.Positioner>
                  </Portal>
                </Select.Root>
              )}
            />
            <Field.ErrorText fontSize={15}>
              {errors.destination?.message}
            </Field.ErrorText>
          </Field.Root>
        </SimpleGrid>
        <Field.Root invalid={!!errors.tourType} mb={6}>
          <Field.Label color="blackAlpha.800">Tour Type</Field.Label>
          <Controller
            name="tourType"
            control={control}
            render={({ field }) => (
              <Select.Root
                key={field.value}
                collection={tourTypes}
                onValueChange={field.onChange}
              >
                <Select.Trigger>
                  <Select.ValueText placeholder="Select tour type" />
                </Select.Trigger>
                <Portal>
                  <Select.Positioner>
                    <Select.Content>
                      {tourTypes.items.map((tourType) => (
                        <Select.Item key={tourType.value} item={tourType}>
                          {tourType.label}
                          <Select.ItemIndicator />
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Positioner>
                </Portal>
              </Select.Root>
            )}
          />
          <Field.ErrorText fontSize={15}>
            {errors.tourType?.message}
          </Field.ErrorText>
        </Field.Root>
        <SimpleGrid columns={{ base: 1, md: 2 }} spaceX={2}>
          <Field.Root invalid={!!errors.participants} mb={6}>
            <Field.Label color="blackAlpha.800">Participants</Field.Label>
            <Input
              type="number"
              {...register("participants", { valueAsNumber: true })}
              min={1}
            />
            <Field.ErrorText fontSize={15}>
              {errors.participants?.message}
            </Field.ErrorText>
          </Field.Root>
          <Field.Root invalid={!!errors.startDate} mb={6}>
            <Field.Label color="blackAlpha.800">Start Date</Field.Label>
            <Input
              type="date"
              {...register("startDate")}
              min={new Date().toISOString().split("T")[0]}
            />
            <Field.ErrorText fontSize={15}>
              {errors.startDate?.message}
            </Field.ErrorText>
          </Field.Root>
        </SimpleGrid>
        <SimpleGrid columns={{ base: 1, md: 2 }} spaceX={2}>
          <Field.Root>
            <Field.Label color="blackAlpha.800">Special Requests</Field.Label>
            <Textarea
              {...register("message")}
              placeholder="Dietary restrictions, accessibility needs, etc..."
            />
          </Field.Root>
        </SimpleGrid>
        <Button
          type="submit"
          bg="green"
          w="full"
          loading={state.submitting}
          loadingText="Submitting..."
          color="gray.50"
          mt={4}
          _hover={{ bg: "green.600" }}
          _active={{ bg: "green.700" }}
          _focus={{ boxShadow: "outline" }}
        >
          Submit Booking Request
        </Button>
      </form>
    </Box>
  );
};

export default BookingForm;
