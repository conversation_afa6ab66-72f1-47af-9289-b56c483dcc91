import { Box, Grid, GridItem, Text, Link, Flex } from "@chakra-ui/react";
import { Phone, Mail, MapPin } from "lucide-react";
import SocialIcons from "./SocialIcons";

const Footer = () => (
  <Box as="footer" bg="gray.800" color="white" py={12}>
    <Grid
      templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }}
      gap={8}
      maxW="1200px"
      mx="auto"
      px={4}
    >
      {/* Company Info */}
      <GridItem>
        <Text fontSize="xl" fontWeight="bold" mb={4}>
          Safari Tanzania
        </Text>
        <Text mb={4}>
          Discover the magic of Tanzania with our expert-led tours. From
          wildlife safaris to beach getaways and mountain adventures.
        </Text>
        <SocialIcons />
      </GridItem>

      {/* Quick Links */}
      <GridItem>
        <Text fontWeight="bold" mb={4}>
          Quick Links
        </Text>
        <Flex direction="column" gap={2}>
          <Link href="/">Home</Link>
          <Link href="#about">About Us</Link>
          <Link href="#tours">Tours & Safaris</Link>
          <Link href="#contact">Destinations</Link>
        </Flex>
      </GridItem>

      {/* Contact Info */}
      <GridItem>
        <Text fontWeight="bold" mb={4}>
          Contact Us
        </Text>
        <Flex direction="column" gap={3}>
          <Flex align="start" gap={3}>
            <MapPin size={20} color="#68D391" />
            <Text color="gray.300">123 Safari Road, Arusha, Tanzania</Text>
          </Flex>
          <Flex align="center" gap={3}>
            <Phone size={20} color="#68D391" />
            <Text color="gray.300">+255 123 456 789</Text>
          </Flex>
          <Flex align="center" gap={3}>
            <Mail size={20} color="#68D391" />
            <Text color="gray.300"><EMAIL></Text>
          </Flex>
        </Flex>
      </GridItem>
    </Grid>

    <Text
      textAlign="center"
      mt={8}
      pt={8}
      borderTop="1px"
      borderColor="gray.700"
    >
      © {new Date().getFullYear()} Safari Tanzania. All rights reserved.
    </Text>
  </Box>
);

export default Footer;
