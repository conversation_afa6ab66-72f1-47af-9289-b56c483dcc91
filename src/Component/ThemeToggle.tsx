import { IconButton } from "@chakra-ui/react";
import { FaSun, FaMoon } from "react-icons/fa";
import { useColorMode } from "@/components/ui/color-mode";

const ThemeToggle = () => {
  const { colorMode, toggleColorMode } = useColorMode();

  return (
    <IconButton
      aria-label="Toggle color mode"
      onClick={toggleColorMode}
      variant="ghost"
      size="md"
      _hover={{
        bg: colorMode === "dark" ? "whiteAlpha.200" : "blackAlpha.100",
        transform: "scale(1.1)",
      }}
      _active={{
        transform: "scale(0.95)",
      }}
      transition="all 0.2s ease-in-out"
    >
      {colorMode === "dark" ? (
        <FaSun size="18px" color="#FDB813" />
      ) : (
        <FaMoon size="18px" color="#4A5568" />
      )}
    </IconButton>
  );
};

export default ThemeToggle;
